This is XeTeX, Version 3.141592653-2.6-0.999997 (MiKTeX 25.4) (preloaded format=xetex 2025.5.27)  2 JUN 2025 13:28
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**./thuthesis.ins
(thuthesis.ins
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\docstrip.tex
\blockLevel=\count28
\emptyLines=\count29
\processedLines=\count30
\commentsRemoved=\count31
\commentsPassed=\count32
\codeLinesPassed=\count33
\TotalprocessedLines=\count34
\TotalcommentsRemoved=\count35
\TotalcommentsPassed=\count36
\TotalcodeLinesPassed=\count37
\NumberOfFiles=\count38
\inFile=\read1
\inputcheck=\read2
\@tempcnta=\count39
\off@0=\count40
\off@1=\count41
\off@2=\count42
\off@3=\count43
\off@4=\count44
\off@5=\count45
\off@6=\count46
\off@7=\count47
\off@8=\count48
\off@9=\count49
\off@10=\count50
\off@11=\count51
\off@12=\count52
\off@13=\count53
\off@14=\count54
\off@15=\count55
\@temptokena=\toks13
\@maxfiles=\count56
\@maxoutfiles=\count57

Utility: `docstrip' v2.6b <2022-09-03>
English documentation    <2024-02-08>

**********************************************************
* This program converts documented macro-files into fast *
* loadable files by stripping off (nearly) all comments! *
**********************************************************

********************************************************
* No Configuration file found, using default settings. *
********************************************************

)

Generating file(s) thuthesis.cls dtx-style.sty 
\openout0 = `thuthesis.cls'.

\openout1 = `dtx-style.sty'.


Processing file thuthesis.dtx (cls) -> thuthesis.cls
                              (dtx-style) -> dtx-style.sty
File thuthesis.dtx ended by \endinput.
Lines  processed: 7029
Comments removed: 2641
Comments  passed: 0
Codelines passed: 4379

 )
No pages of output.
