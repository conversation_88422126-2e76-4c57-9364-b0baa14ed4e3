% !TeX root = ../main.tex

\chapter{总体设计}

在对协议各部分进行设计之前，先要通过理论计算确定或估计一些关键参数。同时，这些参数也是验证协议性能可行性的重要依据。

\section{频率和信道选择}

根据工信部《民用无人驾驶航空器无线电管理暂行办法》的规定，所要设计的协议属于直连通信方式，可以使用1430~1444MHz、2400-2476MHz、5725-5829MHz。
为了避免受到WiFi信号的干扰，同时也为了提高信号的绕射能力，我们选择1430~1444MHz频段。（其中1430~1438MHz频段频率专用于警用飞行器，因此只能从1438~1444MHz频段中选择）

而依据《1430-1444MHz、2400-2476MHz、5725-5829MHz频段民用无人驾驶航空器通信系统无线电发射设备技术要求》\cite{GongYeHeXinXiHuaBu2023a}，1430~1444MHz频段平均分为7个信道，每个信道带宽为2MHz。
在使用软件无线电平台PlutoSDR接收该频段信号时，发现有时在1440MHz频点上可以接收到发射功率。
因此为了避免干扰，最终选择了中心频率为1443MHz，带宽为2MHz的信道（即1442~1444MHz）作为通信协议的物理信道。

\section{符号速率}

我们先将发射机和接收机处的匹配滤波器确定为滚降系数$\alpha=0.35$的根升余弦滤波器。
根据奈奎斯特准则，为了实现无码间串扰且发射能量集中在容许带宽内，最高符号速率应为：
\begin{equation}
    R_s = \frac{2B}{1+\alpha} = \frac{2\times10^6}{1+0.35} = 1.481\times10^6\text{Baud}
\end{equation}
其中$B$为基带带宽，应为1MHz。

为了硬件上频率合成的方便，我们将符号速率选择为1.5MHz，对应的根升余弦滤波器滚降系数则变为：
\begin{equation}
    \alpha = \frac{2B}{R_s} - 1 = \frac{2\times10^6}{1.5\times10^6} - 1 = 0.333
\end{equation}

这样一来，单个符号的周期即为：
\begin{equation}
    T_s = \frac{1}{R_s} = \frac{1}{1.5\times10^6} = 0.667\text{us}
\end{equation}

\section{信噪比}

给定通信协议的最大传输距离为3公里。假设发射机功率为1W，发射机和3公里外的接收机出于同一水平面上，且均使用半波偶极子天线（增益为2.15dB），则接收机处的信号能流密度为：
\begin{equation}
    W_t = D_t W_0 = \frac{D_t P_t}{4\pi d^2} = \frac{10^{2.15/10}\times 1}{4\pi\times(3\times10^3)^2} = 1.45\times10^{-8}\text{W/m}^2
\end{equation}
其中$P_t$为发射机功率，$D_t$为发射机天线增益，$d$为发射机和接收机之间的距离。

接收机接收到的信号功率为：
\begin{equation}
    P_r = A_r W_t = \frac{\lambda^2}{4\pi}D_r W_t = \frac{(3\times 10^8)^2}{4\pi\times(1443\times10^6)^2}\times 10^{2.15/10}\times(1.45\times10^{-8}) = 8.17\times10^{-11}\text{W} = -70.88\text{dBm}
\end{equation}
其中$A_r$为接收机天线的有效孔径，$\lambda$为信号波长，$D_r$为接收机天线增益。

若仅考虑电路热噪声，且假设等效噪声温度为300K，则接收机内部噪声功率为：
\begin{equation}
    N_0 = kT_0B = 1.38\times10^{-23}\times300\times10^6 = 4.14\times10^{-15}\text{W} = -113.83\text{dBm}
\end{equation}
其中$k$为玻尔兹曼常数，$T_0$为等效噪声温度，$B$为接收机带宽。

因此，此时接收机内部的信噪比为
\begin{equation}
    \text{SNR} = \frac{P_r}{N_0} = \frac{8.17\times10^{-11}}{4.14\times10^{-15}} = 1.97\times10^4 = 42.95\text{dB}
\end{equation}
这是理想情况下接收机内部所能达到的最大信噪比。

\section{调制方式}

根据香农定理，使用理想信噪比计算出的信道容量为：
\begin{equation}
    C = B\log_2(1+\text{SNR}) = 10^6\log_2(1+1.97\times10^4) = 1.43\times10^7\text{bit/s}
\end{equation}

在达到香农极限时，每个码元所含的比特数为：
\begin{equation}
    M = \frac{R_b}{R_s} = \frac{C}{R_s} = \frac{1.43\times10^7}{1.5\times10^6} = 9.51
\end{equation}

因此，在理想情况下，信道容量可以支持包括BPSK、QPSK、16QAM、64QAM、256QAM在内的所有常见调制方式。
然而，由于电路设计不足、环境电磁噪声、采样量化误差等诸多不理想因素，信噪比实际不可能达到理想值。
所以，我们选择比特数适中的16QAM作为通信协议的调制方式。这为后续的协议设计留下了裕量，同时也保留了升级到更高阶QAM调制方式的可能性。

基于16QAM调制方式，我们可以反推出最小所需的信道容量和信噪比：
\begin{equation}
    C = M\times R_s = 4\times1.5\times10^6 = 6\times10^6\text{bit/s}
\end{equation}
\begin{equation}
    \text{SNR} = 2^{\frac{C}{B}} - 1 = 2^{\frac{6\times10^6}{10^6}} - 1 = 63 = 17.99\text{dB}
\end{equation}

\section{接收机容量}

根据ASTM F3411-22a标准，每类报文的长度均为25字节，即200比特（详见第5章）。
假设信道编码的比率为1/2（信息比特与编码比特之比为1:2），则每个报文的编码比特数为$200\times2=400$比特。
在使用16QAM调制方式的情况下，每个报文的符号数为$400\div4=100$个，则每条报文正文的传输时间为：
\begin{equation}
    T_{msg} = m T_s = 100\times0.667\text{us} = 66.7\text{us}
\end{equation}
其中$m$为报文的符号数，$T_s$为单个符号的周期。

根据协议最大传输距离3公里计算，单个消息的最大延迟（接收机相对于发射机）为：
\begin{equation}
    T_{delay} = \frac{d}{c} = \frac{3\times10^3}{3\times10^8} = 10\text{us}
\end{equation}
其中$d$为最大传输距离，$c$为光速。

因此，每个报文后的保护间隔至少为10微秒。

由于尚未设计前导码，不能确定其具体长度，不过可以规定其长度不超过报文正文长度的1/3，即$66.7\div 3=22.2\text{us}$。
因此，单个报文的总占用时间为：
\begin{equation}
    T_{total} = T_{pre} + T_{msg} + T_{delay} = 22.2\text{us} + 66.7\text{us} + 10\text{us} = 98.9\text{us}
\end{equation}

《民用微轻小型无人驾驶航空器运行识别最低性能要求（试行）》中规定动态报文
\footnote{即位置向量报文，详见第5章。}
的更新速率至少为1秒1次，如果不考虑冲突碰撞，则目前信道内最大可承载发射机数量为：
\begin{equation}
    N = \frac{1}{T_{total}} = \frac{1}{98.9\times10^{-6}} = 1.01\times 10^4\text{个}
\end{equation}
这也就是协议可承载发射机数量的上限。
