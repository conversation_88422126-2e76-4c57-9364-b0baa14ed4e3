% !TeX root = ../main.tex

\chapter{物理层}

由于频率、符号速率和调制方式均已确定，在物理层我们主要需要设计前导码（Preamble），并利用其完成信号检测、载波同步、符号同步和消息头检测这四大功能，为正常接收正文符号做准备。
在这一部分，我们考察的是一个基带信号数字接收机，其输入为复值采样序列，输出则希望是无误码的消息。

\section{信号检测}

首先，面对一长串消息开始时间未知的信号，接收机需要检测出消息的到来时刻。
为此，我们在前导码的开头设置了2个1比特用于信号与噪声的分离。
下面使用了三种方法来实现信号能量的检测。

\subsection{阈值法}

阈值法是最自然能想到的方法。其工作流程如下：
\begin{itemize}
  \item 在上一个消息结束后进入预热状态，采集连续若干个点（这里使用40个点）作为噪声样本；
  \item 取这些噪声样本的能量（幅度值的平方）的四倍作为阈值，判断信号中是否有能量超过该阈值的样点；
  \item 如果有连续两个符号长度的样点的能量超过阈值，则认为信号到来，否则计数器清零。
\end{itemize}

在实机测试中，阈值法虽然能够应对不同的信号强度，但前提需要噪声功率的长时间稳定。
如果预热期间的噪声功率较小、而之后变大，阈值法则可能将后面的噪声误判为信号。
反之，如果预热期间的噪声功率较大，而信号功率相对较小（小于噪声功率的四倍），那么阈值法就将漏判信号的到来。

究其原因，这是因为阈值法没有充分利用信号和噪声的自相关特性（信号在时域上强相关、而噪声在时域上相关性较小），所以即便可以通过动态阈值调整策略改善其错判、漏判情况，阈值法仍不是信号检测的最优方法。

\subsection{双滑动窗法}

双滑动窗法是适用于突发信号的一种盲检测算法，其则利用了信号和噪声的自相关特性，不需要噪声功率保持稳定，也对信噪比要求不高。

其工作流程如下图所示。时域上有两个相邻的长度相等的滑动窗$W_1$和$W_2$，我们先对两个滑动窗内的样点能量进行求和，然后计算这两个能量和的比值。
\begin{equation}
  A_1 = \sum_{W_1} x(n)^2, \quad A_2 = \sum_{W_2} x(n)^2
\end{equation}
\begin{equation}
  r_{12} = \frac{A_1}{A_2}, \quad r_{21} = \frac{A_2}{A_1}
\end{equation}

若$W_1$和$W_2$窗内均为噪声，则$A_1$和$A_2$均代表了噪声功率，在前后数十个采样点内可以认为是短时稳定的。因此比值$r_{12}$和$r_{21}$的值应该均在1附近。

若$W_1$和$W_2$窗内均为信号，而信号功率恒定，那么比值$r_{12}$和$r_{21}$同样应在1附近。

若$W_1$窗内为噪声、$W_2$窗内为信号（即发射信号开始处），那么由于信号功率远大于平均噪声功率，此时$r_{21}$的值会远大于1，而$r_{12}$的值则会远小于1。

若$W_1$窗内为信号、$W_2$窗内为噪声（即发射信号结束处），同理可得$r_{12}$的值会远大于1，而$r_{21}$的值则会远小于1。

经过上述分析，我们发现比值$r_{21}$在发射信号开始处会出现一个远大于1的峰值，而$r_{12}$则会在发射信号结束处出现一个远大于1的峰值。我们也就可以据此来判断信号开始于结束的时刻。

下面，我们使用一段模拟信号来检验其检测性能。信号由20个0、20个1、20个0的方波比特序列在基带生成，信号通过一个具有频偏、相偏的高斯白噪声信道模型来到接收机处，接收机再对收到的信号进行双滑动窗法能量检测。
频偏设置为10kHz、相偏设置为0.3（理论上不应影响检测结果）、信号与噪声标准差的幅度之比设置为10:1（信噪比为20dB）。仿真结果如下图所示

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/signal_detect.png}
  \caption{双滑动窗法检测结果}
  \label{fig:3-1}
\end{figure}

可以观察到$r_{21}$和$r_{12}$两个比值的确在信号发射开始处和结尾处出现了两个远大于1的峰，而在其余时间内均保持在10以内。
因此，我们将阈值设定为25，即可以判定信号的开始与结束。
此时，为了保证不漏检，信号与噪声的幅度比值需要大于5:1，对应信噪比为14dB，已经低于第2章计算得到的最低信噪比，因此可以认为这个阈值不会漏检协议通信能力范围内的信号。
同时，实机验证也发现，在实际物理环境的噪声中，$r_{21}$和$r_{12}$波动略大于仿真结果，但不会超过25的阈值。因此阈值的设置也很好地避免了将噪声误判为信号。

\subsection{过零率筛选}

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/signal_detect_improved.png}
  \caption{加入过零率筛选后的双滑动窗法检测结果}
  \label{fig:3-2}
\end{figure}

\section{载波同步}

\subsection{Costas环原理简述}

\subsection{仿真结果}

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/lock_time_monotone.png}
  \caption{单频信号的锁定时间}
  \label{fig:3-3}
\end{figure}

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/lock_time_bpsk.png}
  \caption{BPSK调制信号的锁定时间}
  \label{fig:3-4}
\end{figure}

\subsection{同步策略}

\section{符号同步}

\subsection{数字域定时误差估计}

\subsection{符号还原}

\begin{itemize}
  \item 最邻近采样
  \item 相邻采样的线性插值
  \item 拉格朗日插值
\end{itemize}

\subsection{仿真结果}

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/clock_offset.png}
  \caption{时钟同步过程中的符号偏移量}
  \label{fig:3-5}
\end{figure}

\section{消息头检测}

使用7位Barker码进行消息头检测。Barker码具有良好的自相关特性，只在码型完全同步时具有与长度相同的主瓣，其余偏移位置均为0或-1的副瓣，适合用于帧同步。
其码型和自相关特性如下图所示。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/barker.png}
  \caption{Barker码}
  \label{fig:3-6}
\end{figure}
